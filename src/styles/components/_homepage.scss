// Homepage Component Styles - Eneco Brand

.homepage {
  min-height: 100vh;
  background: var(--color-background);
  font-family: var(--font-family-sans);
}

// Header
.homepage__header {
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  padding: var(--spacing-4) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  @include shadow(md);
  border-bottom: 1px solid var(--color-border);
}

.homepage__header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.homepage__logo {
  display: flex;
  align-items: center;
}

.homepage__logo-image {
  height: 60px;
  width: auto;
  object-fit: contain;
}

.homepage__nav {
  display: flex;
  gap: var(--spacing-8);
  
  @include mobile {
    display: none;
  }
}

.homepage__nav-link {
  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  @include transition();

  &:hover {
    color: var(--color-text-brand);
  }
}

.homepage__login-btn {
  background: var(--color-primary);
  color: var(--color-text-inverted);
  border: 1px solid var(--color-primary);
  @include padding-x(4);
  @include padding-y(2);
  @include rounded(md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  @include transition();

  &:hover {
    background: var(--color-primary-hover);
    border-color: var(--color-primary-hover);
  }
}

// Hero Section
.homepage__hero {
  background: linear-gradient(135deg, var(--color-eneco-red) 0%, var(--color-secondary) 100%);
  color: var(--color-text-on-primary);
  padding: var(--spacing-20) 0;
  position: relative;
  overflow: hidden;
}

.homepage__hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.homepage__hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-16);
  align-items: center;
  
  @include mobile {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
  }
}

.homepage__hero-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-6);
  
  @include mobile {
    font-size: var(--font-size-3xl);
  }
}

.homepage__hero-features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--spacing-8) 0;
  
  li {
    margin-bottom: var(--spacing-2);
    font-weight: var(--font-weight-medium);
  }
}

.homepage__hero-form {
  background: white;
  @include padding(6);
  @include rounded(lg);
  @include shadow(xl);
  color: var(--color-text-primary);
}

.homepage__form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  
  @include mobile {
    grid-template-columns: 1fr;
  }
}

.homepage__form-select {
  @include padding(3);
  border: 1px solid var(--color-border);
  @include rounded(md);
  background: white;
  font-size: var(--font-size-base);
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

.homepage__form-actions {
  display: flex;
  gap: var(--spacing-4);
  
  @include mobile {
    flex-direction: column;
  }
}

.homepage__btn {
  @include padding-x(6);
  @include padding-y(3);
  @include rounded(md);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  @include transition();
  border: none;
  font-size: var(--font-size-base);
  
  &--primary {
    background: var(--color-eneco-green);
    color: white;

    &:hover {
      background: var(--color-accent-green-800);
      transform: translateY(-1px);
    }
  }
  
  &--secondary {
    background: transparent;
    color: var(--color-eneco-red);
    border: 2px solid var(--color-eneco-red);

    &:hover {
      background: var(--color-eneco-red);
      color: var(--color-text-on-primary);
    }
  }
}

.homepage__hero-note {
  margin-top: var(--spacing-4);
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.homepage__hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.homepage__person-placeholder {
  width: 300px;
  height: 400px;
  background: linear-gradient(45deg, var(--color-white-opacity-15), var(--color-white-opacity-30));
  @include rounded(lg);
  border: 2px solid var(--color-white-opacity-30);
}

// Services Section
.homepage__services {
  background: white;
  padding: var(--spacing-12) 0;
  border-bottom: 1px solid var(--color-border);
}

.homepage__services-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: var(--spacing-8);
  
  @include mobile {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-4);
  }
}

.homepage__service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--spacing-3);
  @include padding(4);
  @include rounded(lg);
  @include transition();
  cursor: pointer;
  
  &:hover {
    background: var(--color-surface-secondary);
    transform: translateY(-2px);
  }
}

.homepage__service-icon {
  width: 32px;
  height: 32px;
  color: var(--color-eneco-red);
}

.homepage__service-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

// Benefits Section
.homepage__benefits {
  padding: var(--eneco-section-padding) 0;
  background: var(--color-surface-secondary);
}

.homepage__benefits-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.homepage__benefits-title {
  text-align: center;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-16);
}

.homepage__benefits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-8);
  justify-items: center;
  max-width: 1000px;
  margin: 0 auto;

  @include mobile {
    grid-template-columns: 1fr;
  }
}

.homepage__benefit-card {
  background: white;
  @include padding(var(--eneco-card-padding));
  @include rounded(lg);
  @include shadow(md);
  @include transition();
  width: 100%;
  max-width: 450px;

  &:hover {
    @include shadow(lg);
    transform: translateY(-2px);
  }
  
  h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin: var(--spacing-4) 0;
  }
  
  p {
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
  }
}

.homepage__benefit-icon {
  width: 48px;
  height: 48px;
  background: var(--color-eneco-red);
  color: var(--color-text-on-primary);
  @include rounded(lg);
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    width: 24px;
    height: 24px;
  }
}
